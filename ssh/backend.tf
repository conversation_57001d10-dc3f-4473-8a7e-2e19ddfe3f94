locals {
  secrets = jsondecode(data.aws_secretsmanager_secret_version.secrets.secret_string)
}

resource "vault_mount" "ssh_ec2" {
  type = "ssh"
  path = "ssh-certificates"
}

resource "vault_ssh_secret_backend_ca" "ec2" {
  backend     = vault_mount.ssh_ec2.path
  public_key  = local.secrets.vault.ssh.default.public_key
  private_key = local.secrets.vault.ssh.default.private_key

  lifecycle {
    ignore_changes = [public_key, private_key]
  }
}

resource "vault_mount" "ssh_jobs_ec2" {
  type = "ssh"
  path = "jobs-ssh-certificates"
}

resource "vault_ssh_secret_backend_ca" "jobs_ec2" {
  backend     = vault_mount.ssh_jobs_ec2.path
  public_key  = local.secrets.vault.ssh.jobs.public_key
  private_key = local.secrets.vault.ssh.jobs.private_key

  lifecycle {
    ignore_changes = [public_key, private_key]
  }
}
