resource "vault_ssh_secret_backend_role" "admin" {
  backend                 = vault_mount.ssh_ec2.path
  name                    = "admin"
  key_type                = "ca"
  algorithm_signer        = "rsa-sha2-256"
  default_user            = "ubuntu"
  allow_user_certificates = true
  allowed_users           = "*"
  allowed_extensions      = "permit-pty,permit-port-forwarding"
  ttl                     = 1 * 24 * 60 * 60
  max_ttl                 = 7 * 24 * 60 * 60

  default_extensions = {
    "permit-pty" = ""
  }
}

resource "vault_ssh_secret_backend_role" "jobs" {
  backend                 = vault_mount.ssh_jobs_ec2.path
  name                    = "jobs"
  key_type                = "ca"
  algorithm_signer        = "rsa-sha2-256"
  default_user            = "ubuntu"
  allow_user_certificates = true
  allowed_users           = "*"
  allowed_extensions      = "permit-pty,permit-port-forwarding"
  ttl                     = 1 * 24 * 60 * 60
  max_ttl                 = 7 * 24 * 60 * 60

  default_extensions = {
    "permit-pty" = ""
  }
}
