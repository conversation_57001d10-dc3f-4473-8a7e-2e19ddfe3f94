name: "🤖 | Process PR"

on:
  pull_request:
    types: [opened, reopened, closed, edited, unlabeled, ready_for_review]
    branches: [main, release/*]

jobs:
  main-mergeback-pr-opened:
    if: (github.event.action == 'opened' || github.event.action == 'reopened' || github.event.action == 'ready_for_review') && github.event.pull_request.base.ref == 'main' && github.event.pull_request.head.ref == 'release/production' && github.event.pull_request.labels.*.name == 'automation:mergeback'
    runs-on: ubuntu-latest
    steps:
      - name: Enable auto-merge and add label
        env:
          GH_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          gh pr merge ${{ github.event.pull_request.number }} \
            --auto \
            --repo ${{ github.repository }}

  main-change-pr-merged:
    # If a PR is merged into main from a branch that's named as change/xyz, copy the changes
    if: github.event.pull_request.base.ref == 'main' && startsWith(github.event.pull_request.head.ref, 'change/') && github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: COMPLETE THIS

  production-pr-opened:
    if: github.event.pull_request.base.ref == 'release/production' && (github.event.action == 'opened' || github.event.action == 'reopened' || github.event.action == 'ready_for_review')
    permissions:
      issues: write
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Enable auto-merge and add label
        env:
          GH_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          gh pr merge ${{ github.event.pull_request.number }} \
            --auto \
            --squash \
            --repo ${{ github.repository }}

  production-pr-merged:
    if: github.event.pull_request.base.ref == 'release/production' && github.event.pull_request.merged == true
    permissions:
      issues: write
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Create a PR to merge release/production into main
        env:
          GH_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          gh pr create \
            --title "Merge release/production into main" \
            --body "This PR is created automatically by the process-production-merged-pr workflow." \
            --base main \
            --head release/production \
            --label "automation:mergeback" \
            --repo ${{ github.repository }}
