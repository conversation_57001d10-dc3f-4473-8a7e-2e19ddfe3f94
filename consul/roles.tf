resource "vault_consul_secret_backend_role" "admin" {
  backend         = vault_consul_secret_backend.consul.path
  name            = "admin"
  consul_policies = ["global-management"]
}

resource "vault_consul_secret_backend_role" "devops" {
  backend         = vault_consul_secret_backend.consul.path
  name            = "devops"
  consul_policies = ["builtin/global-read-only", consul_acl_policy.devops.name]
}

resource "vault_consul_secret_backend_role" "developer" {
  backend         = vault_consul_secret_backend.consul.path
  name            = "developer"
  consul_policies = [consul_acl_policy.developer.name]
}
