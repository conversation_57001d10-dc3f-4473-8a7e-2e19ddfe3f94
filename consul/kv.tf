locals {
  env = element(split("-", terraform.workspace), 1)
  job_config = {
    dms = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
    }
    therm = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2500
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2500
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
    auth_middleware = {
      development = {
        count  = 3
        cpu    = 1000
        memory = 1024
      }
      qa = {
        count  = 3
        cpu    = 1000
        memory = 1024
      }
      production = {
        count  = 3
        cpu    = 1000
        memory = 1024
      }
    }
    integrations = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
    }
    processing = {
      development = {
        count  = 1
        cpu    = 3000
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 3000
        memory = 2048
      }
      production = {
        count  = 2
        cpu    = 3000
        memory = 2048
      }
    }
    forms = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
    system_model = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2000
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2000
      }
      production = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
    }
    lns = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
    }
    dashboard_exporter = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2500
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2500
      }
      production = {
        count  = 1
        cpu    = 1500
        memory = 2500
      }
    }
    core = {
      development = {
        count  = 1
        cpu    = 2500
        memory = 4000
      }
      qa = {
        count  = 1
        cpu    = 2500
        memory = 4000
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
    storage = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      production = {
        count  = 2
        cpu    = 2500
        memory = 4000
      }
    }
    tasks = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
    pm = {
      development = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
      qa = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
      production = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
    }
    terra = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
    job_provisioner = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
    }
    schedule_parser = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
    }
    cnc = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1500
      }
      production = {
        count  = 1
        cpu    = 2000
        memory = 4000
      }
    }
    approvals = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
    }
    annotations = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
    }
    work = {
      development = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      qa = {
        count  = 1
        cpu    = 1000
        memory = 1024
      }
      production = {
        count  = 1
        cpu    = 1000
        memory = 2000
      }
    }
    inventory = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 1
        cpu    = 4096
        memory = 6000
      }
    }
    naavix = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 1
        cpu    = 3000
        memory = 4096
      }
    }
    onm = {
      development = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      qa = {
        count  = 1
        cpu    = 1500
        memory = 2048
      }
      production = {
        count  = 2
        cpu    = 4096
        memory = 6000
      }
    }
  }
}


resource "consul_key_prefix" "nomad_job_specs" {
  path_prefix = "nomad-job-specs/"

  subkeys = {
    "annotations.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/annotations.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.annotations[local.env]
    })
    "approvals.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/approvals.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.approvals[local.env]
    })
    "auth_middleware.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/auth_middleware.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.auth_middleware[local.env]
    })
    "cnc.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/cnc.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.cnc[local.env]
    })
    "core.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/core.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.core[local.env]
    })
    "dms.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/dms.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.dms[local.env]
    })
    "forms.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/forms.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.forms[local.env]
    })
    "integrations.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/integrations.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.integrations[local.env]
    })
    "inventory.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/inventory.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.inventory[local.env]
    })
    "naavix.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/naavix.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.naavix[local.env]
    })
    "lns.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/lns.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.lns[local.env]
    })
    "pm.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/pm.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.pm[local.env]
    })
    "system_model.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/system_model.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.system_model[local.env]
    })
    "tasks.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/tasks.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.tasks[local.env]
    })
    "terra.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/terra.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.terra[local.env]
    })
    "therm.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/therm.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.therm[local.env]
    })
    "work.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/work.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.work[local.env]
    })
    "storage.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/storage.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.storage[local.env]
    })
    "processing.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/processing.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.processing[local.env]
    })
    "jobs.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/job_provisioner.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.job_provisioner[local.env]
    })
    "schedule_parser.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/schedule_parser.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.schedule_parser[local.env]
    })
    "dashboard_exporter.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/dashboard_exporter.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.dashboard_exporter[local.env]
    })
    "onm.hcl" = templatefile("${path.module}/data/nomad_job_specs/templates/onm.tftpl", {
      env    = local.env
      domain = var.domain
      config = local.job_config.onm[local.env]
    })
  }
}
