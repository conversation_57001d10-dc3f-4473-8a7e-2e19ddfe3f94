
job "naavix" {
  datacenters = ["${env}"]
  type        = "service"

  constraint {
    attribute = "$${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "naavix" {
    shutdown_delay = "1m"
    count          = ${config.count}
    ephemeral_disk {
      size    = 5000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "naavix"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "naavix",
        "traefik.enable=true",
        "traefik.http.routers.naavix-api.rule= Host(`api.${domain}`) && PathPrefix(`/naavix/`)",
        "traefik.http.middlewares.strip-naavix-api.stripprefix.prefixes=/naavix/",
        "traefik.http.routers.naavix-api.middlewares=strip-naavix-api",
        "traefik.http.routers.naavix-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "naavix" {
      driver = "docker"
      config {
        image = "sensehawk/naavix_${env}:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "naavix"
          "com.datadoghq.tags.env" = "${env}"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"naavix\", \"service\": \"naavix\", \"path\": \"/var/log/naavix.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-naavix"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs/naavix" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{with secret "aws/static-creds/naavix-service"}}
AWS_ACCESS_KEY_ID={{ .Data.access_key }}
AWS_SECRET_ACCESS_KEY={{ .Data.secret_key }}
{{ end }}
{{with secret "postgres/static-creds/naavix-service"}}
DB_USERNAME={{ .Data.username }}
DB_PASSWORD={{ .Data.password }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/naavix" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = ${config.cpu}  # MHz
        memory = ${config.memory}  # MB
      }
    }
  }
}
