
job "system_model" {
  datacenters = ["${env}"]
  type        = "service"

  constraint {
    attribute = "$${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "system_model" {
    shutdown_delay = "1m"
    count          = ${config.count}
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "system-model"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "system_model",
        "traefik.enable=true",
        "traefik.http.routers.system_model-api.rule= Host(`api.${domain}`) && PathPrefix(`/system-model/`)",
        "traefik.http.middlewares.strip-system_model-api.stripprefix.prefixes=/system-model/",
        "traefik.http.routers.system_model-api.middlewares=strip-system_model-api",
        "traefik.http.routers.system_model-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "system_model" {
      driver = "docker"
      config {
        image = "sensehawk/system_model_${env}:{{HASH}}"
        labels = {
            "com.datadoghq.tags.service" = "system-model"
            "com.datadoghq.tags.env" = "${env}"
            "com.datadoghq.tags.version" = "{HASH}"
            "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"system_model\", \"service\": \"system_model\", \"path\": \"/var/log/system_model.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-system_model"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs/system_model" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{with secret "postgres/static-creds/system_model-service"}}
DB_USERNAME={{ .Data.username }}
DB_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "postgres/static-creds/readonly-service"}}
POSTGRES_READONLY_USERNAME={{ .Data.username }}
POSTGRES_READONLY_PASSWORD={{ .Data.password }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/system_model" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = ${config.cpu}  # MHz
        memory = ${config.memory}  # MB
      }
    }
  }
}
