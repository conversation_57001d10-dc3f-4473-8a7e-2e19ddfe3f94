job "auth_middleware" {
    datacenters = ["${env}"]
    type = "service"
  
    constraint {
      attribute = "$${meta.type}"
      value     = "server"
    }
    
    update {
      auto_revert = true
    }
  
  
    group "auth_middleware" {
      count = ${config.count}
      ephemeral_disk {
        size    = 2000
      }
      constraint {
        distinct_hosts = true
      }
  
      network {
        mode = "bridge"
        port "server" {
          static = 3333
          to = 3333
        }
      }
  
      task "auth_middleware" {
        service {
          name = "auth-middleware"
          tags = ["global", "commit={{HASH}}", "time={{TIME}}", "auth_middleware"]
          port = "server"

          check {
           name     = "alive"
           type     = "http"
           port     = "server"
           path     = "/ping"
           interval = "60s"
           timeout  = "2s"
         }
        }
        driver = "docker"
        config {
          image = "sensehawk/auth_middleware_${env}:{{HASH}}"
          labels = {
            "com.datadoghq.tags.service" = "auth_middleware"
            "com.datadoghq.tags.env" = "${env}"
            "com.datadoghq.tags.version" = "{{HASH}}"
            "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"auth_middleware\", \"service\": \"auth_middleware\", \"path\": \"/var/log/applications/auth_middleware.log\"}]"
          }
          ports = ["server"]
          auth {
              username = "sensehawk"
              password = "{{DOCKERHUB_TOKEN}}"
          }
        }

        vault {
          policies = ["service-auth_middleware"]
        }

        template {
  			data        = <<EOH
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs/auth_middleware" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
      	destination = "secrets/.env"
        env = true
			}
        resources {
          cpu    = ${config.cpu}
          memory =  ${config.memory}
        }
      }
    }
  }
