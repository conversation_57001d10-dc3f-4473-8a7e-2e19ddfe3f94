job "dashboard_exporter" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "dashboard_exporter" {
    shutdown_delay = "1m"
    count          = 1
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 1337
      }
      port "dashboard" {
        to = 8000
      }
    }

    service {
      name = "dashboard-exporter"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "dashboard_exporter",
        "traefik.enable=true",
        "traefik.http.routers.dashboard_exporter-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/dashboard-exporter/`)",
        "traefik.http.middlewares.strip-dashboard_exporter-api.stripprefix.prefixes=/dashboard-exporter/",
        "traefik.http.routers.dashboard_exporter-api.middlewares=strip-dashboard_exporter-api",
        "traefik.http.routers.dashboard_exporter-api.entrypoints=websecure"
      ]
      port = "dashboard"

      check {
        name     = "alive"
        type     = "http"
        port     = "dashboard"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }
    task "fusion_export_server" {
      driver = "docker"
      config {
        image = "sensehawk/fusion-export-server_development:latest"
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
        ports = ["server"]
      }
      vault {
        policies = ["service-dashboard_exporter"]
      }
      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/dashboard_exporter" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 2000  # MHz
        memory = 2500  # MB
      }
    }
    task "fusion_export_dashboard" {
      env {
        	FUSION_EXPORTER_HOST = "${NOMAD_HOST_IP_server}"
          FUSION_EXPORTER_PORT = "${NOMAD_HOST_PORT_server}" 
        }
      driver = "docker"
      config {
        image = "sensehawk/dashboard-exporter_development:{{HASH}}"
        labels = {
            "com.datadoghq.tags.service" = "dashboard_exporter"
            "com.datadoghq.tags.env" = "development"
            "com.datadoghq.tags.version" = "{HASH}"
            "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"dashboard_exporter\", \"service\": \"dashboard_exporter\", \"path\": \"/var/log/dashboard_exporter.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["dashboard"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-dashboard_exporter"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{with secret "aws/creds/dashboard_exporter-service"}}
AWS_ACCESS_KEY_ID={{ .Data.access_key }}
AWS_SECRET_ACCESS_KEY={{ .Data.secret_key }}
{{ end }}
{{ with secret "keys/data/secrets/dashboard_exporter" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 2000  # MHz
        memory = 2000  # MB
      }
    }
  }
}