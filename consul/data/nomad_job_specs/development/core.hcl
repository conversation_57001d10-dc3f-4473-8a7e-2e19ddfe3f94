job "core" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "core" {
    shutdown_delay = "1m"
    count          = 1
		ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "core"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "core",
        "traefik.enable=true",
        "traefik.http.routers.core-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/core/`)",
        "traefik.http.middlewares.strip-core-api.stripprefix.prefixes=/core/",
        "traefik.http.routers.core-api.middlewares=strip-core-api",
        "traefik.http.routers.core-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "core" {
      driver = "docker"
      config {
        image = "sensehawk/core_development:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "core"
          "com.datadoghq.tags.env" = "development"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"core\", \"service\": \"core\", \"path\": \"/var/log/core.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-core"]
      }

      template {
  			data        = <<EOH
{{ with nomadVar "nomad/jobs/core" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON}}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON}}
{{ end }}
{{ end }}
{{with secret "postgres/static-creds/core-service"}}
DB_USERNAME={{ .Data.username }}
DB_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "postgres/static-creds/readonly-service"}}
POSTGRES_READONLY_USERNAME={{ .Data.username }}
POSTGRES_READONLY_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "aws/creds/core-service"}}
AWS_ACCESS_KEY_ID={{ .Data.access_key }}
AWS_SECRET_ACCESS_KEY={{ .Data.secret_key }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON}}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/core" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON}}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
      	destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
			}

      resources {
        cpu    = 4000  # MHz
        memory = 4000  # MB
      }
    }
  }
}