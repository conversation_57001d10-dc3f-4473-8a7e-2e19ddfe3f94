job "storage" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "storage" {
    shutdown_delay = "1m"
    count          = 1
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "storage"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "storage",
        "traefik.enable=true",
        "traefik.http.routers.storage-host.rule=Host(`storage.dev-taskmapper.com`)",
        "traefik.http.routers.storage-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/storage/`)",
        "traefik.http.middlewares.strip-storage-api.stripprefix.prefixes=/storage/",
        "traefik.http.routers.storage-api.middlewares=strip-storage-api",
        "traefik.http.routers.storage-api.entrypoints=websecure",
        "traefik.http.routers.storage-host.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "storage" {
      driver = "docker"
      config {
        image = "sensehawk/storage_development:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "storage"
          "com.datadoghq.tags.env" = "development"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"storage\", \"service\": \"storage\", \"path\": \"/var/log/storage.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-storage"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs/storage" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{with secret "aws/creds/storage-service"}}
AWS_ACCESS_KEY_ID={{ .Data.access_key }}
AWS_SECRET_ACCESS_KEY={{ .Data.secret_key }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/storage" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 1000 # MHz
        memory = 1024 # MB
      }
    }
  }
}