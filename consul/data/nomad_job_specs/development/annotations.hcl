job "annotations" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "annotations" {
    shutdown_delay = "1m"
    count          = 1
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "annotations"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "annotations",
        "traefik.enable=true",
        "traefik.http.routers.annotations-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/annotations/`)",
        "traefik.http.middlewares.strip-annotations-api.stripprefix.prefixes=/annotations/",
        "traefik.http.routers.annotations-api.middlewares=strip-annotations-api",
        "traefik.http.routers.annotations-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "annotations" {
      driver = "docker"
      config {
        image = "sensehawk/annotations_development:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "annotations"
          "com.datadoghq.tags.env" = "development"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"annotations\", \"service\": \"annotations\", \"path\": \"/var/log/annotations.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-annotations"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs/annotations" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{with secret "mongo/static-creds/annotations-service"}}
DB_USERNAME={{ .Data.username }}
DB_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "postgres/static-creds/readonly-service"}}
POSTGRES_READONLY_USERNAME={{ .Data.username }}
POSTGRES_READONLY_PASSWORD={{ .Data.password }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 500  # MHz
        memory = 1000  # MB
      }
    }
  }
}