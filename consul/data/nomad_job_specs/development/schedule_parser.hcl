
job "schedule_parser" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "schedule_parser" {
    shutdown_delay = "1m"
    count          = 1
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "schedule-parser"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "schedule_parser",
        "traefik.enable=true",
        "traefik.http.routers.schedule_parser-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/schedule-parser/`)",
        "traefik.http.middlewares.strip-schedule_parser-api.stripprefix.prefixes=/schedule-parser/",
        "traefik.http.routers.schedule_parser-api.middlewares=strip-schedule_parser-api",
        "traefik.http.routers.schedule_parser-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "schedule_parser" {
      driver = "docker"
      config {
        image = "sensehawk/schedule-parser_development:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "schedule-parser"
          "com.datadoghq.tags.env" = "development"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"schedule_parser\", \"service\": \"schedule_parser\", \"path\": \"/var/log/schedule_parser.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-schedule_parser"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 1500  # MHz
        memory = 1500  # MB
      }
    }
  }
}
