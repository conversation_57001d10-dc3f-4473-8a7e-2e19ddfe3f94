
job "job_provisioner" {
  datacenters = ["development"]
  type        = "service"

  constraint {
    attribute = "${meta.type}"
    value     = "client"
  }

  update {
    auto_revert      = true
    canary           = 1
    auto_promote     = true
  }

  group "job_provisioner" {
    shutdown_delay = "1m"
    count          = 1
    ephemeral_disk {
      size    = 2000
    }
    network {
      mode = "bridge"
      port "server" {
        to = 8000
      }
    }

    service {
      name = "job-provisioner"
      tags = [
        "global", "commit={{HASH}}", "time={{TIME}}", "job_provisioner",
        "traefik.enable=true",
        "traefik.http.routers.job_provisioner-api.rule= Host(`api.dev-taskmapper.com`) && PathPrefix(`/jobs/`)",
        "traefik.http.middlewares.strip-job_provisioner-api.stripprefix.prefixes=/jobs/",
        "traefik.http.routers.job_provisioner-api.middlewares=strip-job_provisioner-api",
        "traefik.http.routers.job_provisioner-api.entrypoints=websecure"
      ]
      port = "server"

      connect {
        sidecar_service {
          tags = ["sidecar"]
          proxy {
            local_service_port = 8000
            transparent_proxy {
            	exclude_inbound_ports = [8000]
            }
          }
        }
      }

      check {
        name     = "alive"
        type     = "http"
        port     = "server"
        path     = "/v1/resource/health/"
        interval = "5s"
        timeout  = "2s"
      }
    }

    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    task "job_provisioner" {
      driver = "docker"
      config {
        image = "sensehawk/job-provisioner_development:{{HASH}}"
        labels = {
        	"com.datadoghq.tags.service" = "job_provisioner"
          "com.datadoghq.tags.env" = "development"
          "com.datadoghq.tags.version" = "{{HASH}}"
          "com.datadoghq.ad.logs" = "[{\"type\":\"file\", \"source\": \"job_provisioner\", \"service\": \"job_provisioner\", \"path\": \"/var/log/job_provisioner.log\"}]"
        }
        volumes = [
          "/var/log:/var/log",
        ]
        ports = ["server"]
        auth {
          username = "sensehawk"
          password = "{{DOCKERHUB_TOKEN}}"
        }
      }

      vault {
        policies = ["service-job_provisioner"]
      }

      template {
        data        = <<EOH
{{ with nomadVar "nomad/jobs/job_provisioner" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{ with nomadVar "nomad/jobs" }}
{{ range .Tuples }}
{{ .K }}={{ .V | toJSON }}
{{ end }}
{{ end }}
{{with secret "mongo/static-creds/job_provisioner-service"}}
DB_USERNAME={{ .Data.username }}
DB_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "postgres/static-creds/readonly-service"}}
POSTGRES_READONLY_USERNAME={{ .Data.username }}
POSTGRES_READONLY_PASSWORD={{ .Data.password }}
{{ end }}
{{with secret "aws-jobprovisioner/creds/job_provisioner-service"}}
AWS_ACCESS_KEY_ID={{ .Data.access_key }}
AWS_SECRET_ACCESS_KEY={{ .Data.secret_key }}
{{ end }}
{{ with secret "keys/data/secrets/shared" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
{{ with secret "keys/data/secrets/job_provisioner" }}
{{ range $key, $value := .Data.data }}
{{ $key }}={{ $value | toJSON }}
{{ end }}
{{ end }}
COMMIT_SHA = "{{HASH}}"
EOH
        destination = "secrets/.env"      # TODO: Change this to secrets/.env in production
        env = true
      }

      resources {
        cpu    = 3000  # MHz
        memory = 2048  # MB
      }
    }
  }
}
