#Ignore .env files
.env*

#Ignore all secret files
*.secrets.*

# Ignore all pem files
*.pem

# Ignore all zip files
*.zip

# Ignore .pub files
*.pub

# Ignore .DS_Store files from every directory
.DS_Store

# Ignore vscode settings
.vscode/

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data, such as
# password, private keys, and other secrets. These should not be part of version 
# control as they are data points which are potentially sensitive and subject 
# to change depending on the environment.
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore transient lock info files created by terraform apply
.terraform.tfstate.lock.info

# Ignore terraform lock hcl files
.terraform.lock.hcl

# Ignore terraform state files
.terraform

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc