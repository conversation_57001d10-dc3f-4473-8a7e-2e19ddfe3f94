resource "vault_okta_auth_backend_group" "admin" {
  path       = vault_okta_auth_backend.okta.path
  group_name = "${var.prefix}-admin"
  policies   = ["default", "admin"]
}

resource "vault_okta_auth_backend_group" "devops" {
  path       = vault_okta_auth_backend.okta.path
  group_name = "${var.prefix}-devops"
  policies   = ["default", "devops"]
}

resource "vault_okta_auth_backend_group" "developer" {
  path       = vault_okta_auth_backend.okta.path
  group_name = "${var.prefix}-developer"
  policies   = ["default", "developer"]
}
