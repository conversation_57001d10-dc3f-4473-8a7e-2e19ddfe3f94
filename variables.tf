variable "secrets_name" {
  type        = string
  description = "Name of the AWS secrets manager secret"
  default     = "config-secrets"
}

variable "prefix" {
  type        = string
  description = "Environment prefix"
}

variable "region" {
  type        = string
  description = "AWS Region of the primary infrastructure"
}

variable "account_id" {
  type        = string
  description = "AWS Account ID"
}

variable "domain" {
  type        = string
  description = "Domain name for the Consul, Nomad and Vault servers"
}

variable "app_aliases" {
  type        = list(string)
  description = "List of app aliases to create DNS entries for"
  default     = []
}

variable "aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID"
  sensitive   = false
}

variable "aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key"
  sensitive   = true
}

variable "jobprovisioner_aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID for jobprovisioner"
  sensitive   = false
}

variable "jobprovisioner_aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key for jobprovisioner"
  sensitive   = true
}

variable "pilot_aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID for pilot"
  sensitive   = false
}

variable "pilot_aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key for pilot"
  sensitive   = true
}

variable "core_old_buckets" {
  type        = list(string)
  description = "List of old core bucket names"
}

variable "pilot_old_buckets" {
  type        = list(string)
  description = "List of old pilot buckets"
}

variable "mbtiles_old_bucket" {
  type        = string
  description = "Name of the old mbtiles bucket"
}

variable "public_old_bucket" {
  type        = string
  description = "Name of the old public bucket"
}

variable "vault_consul_http_token" {
  type        = string
  description = "Consul HTTP token for vault to use"
}

variable "postgres_host" {
  description = "The hostname or IP address of the PostgreSQL server"
  type        = string
}

variable "postgres_username" {
  description = "The username for connecting to the PostgreSQL database"
  type        = string
}

variable "postgres_password" {
  description = "The password for the PostgreSQL user"
  type        = string
  sensitive   = true
}

variable "mongodb_atlas_project_id" {
  type        = string
  description = "The project ID for the MongoDB Atlas cluster"
}

variable "mongodb_atlas_public_key" {
  type        = string
  description = "The public key for the MongoDB Atlas cluster"
}

variable "mongodb_atlas_private_key" {
  type        = string
  description = "The private key for the MongoDB Atlas cluster"
  sensitive   = true
}

variable "vault_nomad_token" {
  type        = string
  description = "Nomad HTTP token for vault to use"
}


variable "okta_api_token" {
  type        = string
  description = "Okta API token"
}

variable "okta_organization" {
  type        = string
  description = "Okta organization"
}

variable "okta_base_url" {
  type        = string
  description = "Okta base url"
}
