import sys

old_role = sys.argv[1]
role = sys.argv[2]
old_db_name = sys.argv[3]
db_name = sys.argv[4]

statements = f"""ALTER ROLE {old_role} RENAME TO {role};
GRANT {role} TO postgres;
ALTER DATABASE {old_db_name} RENAME TO {db_name};

DO $$
DECLARE
    db_name TEXT := '{db_name}'; -- Specify the database name here
    role_name TEXT;
BEGIN
    -- Revoke all privileges from the PUBLIC role
    EXECUTE format('REVOKE ALL ON DATABASE %I FROM PUBLIC', db_name);

    -- Loop through all roles with privileges on the specified database and revoke them
    FOR role_name IN
        SELECT r.rolname
        FROM pg_roles r
        JOIN pg_shdepend d ON r.oid = d.refobjid
        JOIN pg_database db ON d.objid = db.oid
        WHERE db.datname = db_name
    LOOP
        EXECUTE format('REVOKE ALL ON DATABASE %I FROM %I', db_name, role_name);
    END LOOP;

    -- Grant CONNECT and TEMP privileges back to the PUBLIC role
    EXECUTE format('GRANT CONNECT, TEMP ON DATABASE %I TO PUBLIC', db_name);
END $$;"""

print(statements)