import sys
import uuid

old_role = sys.argv[1]
role = sys.argv[2]
old_db_name = sys.argv[3]
db_name = sys.argv[4]
password = str(uuid.uuid4())

# print(password)

statements = f"""
\c {old_db_name};
--------------------------------------------------------------------------------
CREATE ROLE "{role}" WITH LOGIN PASSWORD '{password}';
GRANT USAGE, CREATE ON SCHEMA public TO {role};
GRANT ALL ON SCHEMA public TO {role};

ALTER DATABASE {old_db_name} OWNER TO {role};
GRANT {role} TO postgres;
--------------------------------------------------------------------------------
DO $$ 
DECLARE 
    obj RECORD;
BEGIN
    FOR obj IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public' AND tableowner = '{old_role}'
    LOOP
        EXECUTE format('ALTER TABLE public.%I OWNER TO {role};', obj.tablename);
    END LOOP;
END $$;
--------------------------------------------------------------------------------
DO $$
DECLARE
    func RECORD;
BEGIN
    FOR func IN 
        SELECT 
            n.nspname AS schema_name,
            p.proname AS function_name,
            pg_get_function_identity_arguments(p.oid) AS func_args
        FROM 
            pg_proc p
        JOIN 
            pg_namespace n ON n.oid = p.pronamespace
        WHERE 
            p.proowner = (SELECT oid FROM pg_roles WHERE rolname = '{old_role}')
    LOOP
        EXECUTE 'ALTER FUNCTION ' || func.schema_name || '.' || func.function_name || 
                '(' || func.func_args || ') OWNER TO {role};';
    END LOOP;
END $$;
--------------------------------------------------------------------------------
DO $$
DECLARE
    db_name TEXT := '{old_db_name}'; -- Specify the database name here
    role_name TEXT;
BEGIN
    -- Revoke all privileges from the PUBLIC role
    EXECUTE format('REVOKE ALL ON DATABASE %I FROM PUBLIC', db_name);

    -- Loop through all roles with privileges on the specified database and revoke them
    FOR role_name IN
        SELECT r.rolname
        FROM pg_roles r
        JOIN pg_shdepend d ON r.oid = d.refobjid
        JOIN pg_database db ON d.objid = db.oid
        WHERE db.datname = db_name
    LOOP
        EXECUTE format('REVOKE ALL ON DATABASE %I FROM %I', db_name, role_name);
    END LOOP;

    -- Grant CONNECT and TEMP privileges back to the PUBLIC role
    EXECUTE format('GRANT CONNECT, TEMP ON DATABASE %I TO PUBLIC', db_name);
END $$;
--------------------------------------------------------------------------------
SELECT 
    n.nspname AS schema_name,
    p.proname AS function_name,
    r.rolname AS owner
FROM 
    pg_proc p
JOIN 
    pg_namespace n ON n.oid = p.pronamespace
JOIN 
    pg_roles r ON r.oid = p.proowner
WHERE 
    r.rolname = '{role}';

--------------------------------------------------------------------------------
\c postgres;
--------------------------------------------------------------------------------
ALTER DATABASE {old_db_name} RENAME TO {db_name};
"""

print(statements)