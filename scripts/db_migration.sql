-- Rename database owners
ALTER ROLE <old_role_name> RENAME TO <role_name>;
GRANT <role_name> TO postgres;
ALTER DATABASE <old_db_name> RENAME TO <db_name>;

DO $$
DECLARE
    db_name TEXT := '<db_name>'; -- Specify the database name here
    role_name TEXT;
BEGIN
    -- Revoke all privileges from the PUBLIC role
    EXECUTE format('REVOKE ALL ON DATABASE %I FROM PUBLIC', db_name);

    -- Loop through all roles with privileges on the specified database and revoke them
    FOR role_name IN
        SELECT r.rolname
        FROM pg_roles r
        JOIN pg_shdepend d ON r.oid = d.refobjid
        JOIN pg_database db ON d.objid = db.oid
        WHERE db.datname = db_name
    LOOP
        EXECUTE format('REVOKE ALL ON DATABASE %I FROM %I', db_name, role_name);
    END LOOP;

    -- Grant CONNECT and TEMP privileges back to the PUBLIC role
    EXECUTE format('GRANT CONNECT, TEMP ON DATABASE %I TO PUBLIC', db_name);
END $$;


--------------------------------------------------------------------------------


-- Function user
CREATE ROLE functions_read_only WITH LOGIN PASSWORD 'test';
-- GRANT CONNECT ON DATABASE postgres TO functions_read_only;
-- GRANT CONNECT ON DATABASE core_production TO functions_read_only;
\c <db_name>
GRANT USAGE ON SCHEMA public TO execute_functions;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO execute_functions;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO execute_functions;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO execute_functions;

ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE <db_owner> GRANT SELECT ON TABLES TO execute_functions;
ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE <db_owner> GRANT SELECT ON SEQUENCES TO execute_functions;
ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE <db_owner> GRANT EXECUTE ON FUNCTIONS TO execute_functions;