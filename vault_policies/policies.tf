
locals {
  services = ["work", "storage", "auth_middleware", "core", "terra", "naavix", "inventory", "system_model", "processing", "therm", "tasks", "forms", "dms", "annotations", "pm", "approvals", "integrations", "lns", "cnc", "onm", "job_provisioner", "schedule_parser", "dashboard_exporter"]
}

resource "vault_policy" "service" {
  for_each = toset(local.services)
  name     = "service-${each.value}"

  policy = <<EOT
path "keys/data/secrets/shared" {
  capabilities = ["read"]
}

path "keys/data/secrets/${each.value}" {
  capabilities = ["read"]
}

path "${each.value == "job_provisioner" ? "aws-jobprovisioner" : "aws"}/creds/${each.value}-service" {
  capabilities = ["read"]
}

path "${each.value == "job_provisioner" ? "aws-jobprovisioner" : "aws"}/static-creds/${each.value}-service" {
  capabilities = ["read"]
}

path "postgres/static-creds/readonly-service" {
  capabilities = ["read"]
}

path "postgres/static-creds/${each.value}-service" {
  capabilities = ["read"]
}

path "mongo/static-creds/${each.value}-service" {
  capabilities = ["read"]
}
EOT
}

resource "vault_policy" "admin" {
  name = "admin"

  policy = <<EOT

path "*" {
  capabilities = ["read", "list"]
}

path "keys/*" {
  capabilities = ["create", "read", "update", "patch", "delete", "list"]
}

path "ssh-certificates/sign/*" {
  capabilities = ["update"]
}

path "jobs-ssh-certificates/sign/*" {
  capabilities = ["update"]
}

path "sys/leases/lookup/" {
  capabilities = ["sudo", "list"]
}

path "sys/leases/*" {
  capabilities = ["sudo", "create", "read", "update", "patch", "delete", "list"]
}
path "mongo/creds/admin" {
  capabilities = ["read"]
}
EOT
}

resource "vault_policy" "devops" {
  name = "devops"

  policy = <<EOT
path "*" {
  capabilities = ["list"]
}

path "keys/*" {
  capabilities = ["read", "list"]
}

path "ssh-certificates/sign/*" {
  capabilities = ["update"]
}

path "jobs-ssh-certificates/sign/*" {
  capabilities = ["update"]
}

path "consul/creds/devops" {
  capabilities = ["read"]
}

path "nomad/creds/devops" {
  capabilities = ["read"]
}

path "nomad/creds/developer" {
  capabilities = ["read"]
}

path "nomad/creds/developer-exec" {
  capabilities = ["read"]
}
path "mongo/creds/read-write" {
  capabilities = ["read"]
}
EOT
}

resource "vault_policy" "developer" {
  name = "developer"

  policy = <<EOT
path "consul/creds/developer" {
  capabilities = ["read"]
}

path "nomad/creds/developer" {
  capabilities = ["read"]
}

path "jobs-ssh-certificates/sign/*" {
  capabilities = ["update"]
}
path "mongo/creds/read-only" {
  capabilities = ["read"]
}
EOT
}
