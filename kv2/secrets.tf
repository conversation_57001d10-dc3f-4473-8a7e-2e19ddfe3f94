locals {
  env     = split("-", terraform.workspace)[1]
  secrets = jsondecode(data.aws_secretsmanager_secret_version.secrets.secret_string)

  services = {
    shared             = local.secrets.vault.kv.shared
    core               = local.secrets.vault.kv.core
    terra              = local.secrets.vault.kv.terra
    system_model       = local.secrets.vault.kv.system_model
    processing         = local.secrets.vault.kv.processing
    inventory          = local.secrets.vault.kv.inventory
    naavix             = local.secrets.vault.kv.naavix
    job_provisioner    = local.secrets.vault.kv.job_provisioner
    storage            = local.secrets.vault.kv.storage
    dashboard_exporter = local.secrets.vault.kv.dashboard_exporter
  }
}

resource "vault_kv_secret_v2" "secrets" {
  for_each = local.services

  mount     = vault_mount.kvv2.path
  name      = "secrets/${each.key}"
  data_json = jsonencode(each.value)
}
