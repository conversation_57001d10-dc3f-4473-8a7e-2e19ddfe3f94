locals {
  env = split("-", terraform.workspace)[1]
}

locals {
  shared = {
    IS_NEW_INFRA = {
      default = "true"
    }
    DOMAIN = {
      default = var.domain
    }
    CONSUL_DOMAIN = {
      development = "dev_taskmapper"
      qa          = "qa_taskmapper"
      production  = "taskmapper"
    }
    ENV = {
      development = "development"
      qa          = "qa"
      production  = "production"
    }
    AWS_REGION = {
      default = var.region
    }
    ACTIVEMQ_HOST = {
      default = "stomp.amq.${var.domain}"
    }
    REDIS_HOST = {
      development = "master.tm-dev-redis-cache.4xkbra.use1.cache.amazonaws.com"
      qa          = "master.tm-qa-redis-cache.swe3di.use1.cache.amazonaws.com"
      production  = "master.tm-prod-redis-cache.zqoxoo.use1.cache.amazonaws.com"
    }
    POSTGRES_HOST = {
      development = "tm-dev-postgresql.cvmqiuyyuo73.us-east-1.rds.amazonaws.com"
      qa          = "tm-qa-postgresql.c6bc8cuootkd.us-east-1.rds.amazonaws.com"
      production  = "tm-prod-postgresql.ci1e6coom236.us-east-1.rds.amazonaws.com"
    }
    MONGODB_HOST = {
      development = "temp-development-pl-0.2fl75.mongodb.net"
      qa          = "qa-pl-0.6cdw3.mongodb.net"
      production  = "production-pl-0.yzjfp.mongodb.net"
    }
    GENERATE_THUMBNAILS_URL = {
      development = "https://qn73jyufs2e2pmlglo4hw7wsja0cusgk.lambda-url.us-east-1.on.aws/"
      qa          = "https://dq7ecr7w4rcgxmybnelsdokcc40swrvq.lambda-url.us-east-1.on.aws/"
      production  = "https://oaknnqc2rbzneuh5wifygljgvm0xtfmi.lambda-url.us-east-1.on.aws/"

    }
    GENERATE_PDF_URL = {
      development = "https://dubtaxw7cji7kybcztmngrxy7q0muxkh.lambda-url.us-east-1.on.aws/"
      qa          = "https://4b7q7xgsawq4et457il22rs6qe0aivde.lambda-url.us-east-1.on.aws/"
      production  = "https://ni3aqzgms7wywnbksdlih6vdhy0lrpzo.lambda-url.us-east-1.on.aws/"
    }
    GENERATE_PDF_ZIP_URL = {
      development = "https://qvdvy5inflrwmmbvnhbbfixgn40xiazn.lambda-url.us-east-1.on.aws/"
      qa          = "https://yj3vb4whcqlx5qsm4bj5sfzjva0ejrjs.lambda-url.us-east-1.on.aws/"
      production  = "https://3x7e6gnc3pz2nvqov5nypzuvri0lixuh.lambda-url.us-east-1.on.aws/"
    }
    SCHEDULER_CLUSTER = {
      development = "Temp-Development"
      qa          = "Temp-QA"
      production  = "Temp-Production"
    }
    SCHEDULER_DATABASE = {
      development = "scheduler_staging"
      qa          = "scheduler_staging"
      production  = "scheduler"
    }
    SCHEDULER_COLLECTION = {
      development = "hooks"
      qa          = "hooks"
      production  = "hooks"
    }
    SENDGRID_FROM_EMAIL = {
      default = "noreply@${var.domain}"
    }
    SENDGRID_FROM_NAME = {
      default = "TaskMapper"
    }
    BOT_X_USER_DATA = {
      development = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      qa          = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      production  = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }
    X_USER_DATA_PREFIX = {
      default = ""
    }
    AMQ_PREFIX = {
      development = "DEV"
      qa          = "TEST"
      production  = "SENSEHAWK"
    }
    BUCKET = {
      default = "${var.prefix}-primary"
    }
    PUBLIC_BUCKET = {
      default = "${var.prefix}-public"
    }
    MBTILES_BUCKET = {
      default = "${var.prefix}-mbtiles"
    }
    MAPSERVER_URL_V1 = {
      default = "https://mapserver.sensehawk.com/"
    }
    MAPSERVER_URL_V2 = {
      development = "https://maps-dev-server.sensehawk.com/"
      qa          = "https://maps-qa-server.sensehawk.com/"
      production  = "https://maps-server.sensehawk.com/"
    }
    MAPSERVER_URL_V3 = {
      default = "https://maps-server.${var.domain}/"
    }
    DD_ENV = {
      development = "development"
      qa          = "qa"
      production  = "production"
    }
    DD_TRACE_DEBUG = {
      default = "false"
    }
    DD_LOGS_INJECTION = {
      default = "true"
    }
    DD_TRACE_ENABLED = {
      default = "true"
    }
    DD_APM_ENABLED = {
      default = "true"
    }
    DD_LOGS_ENABLED = {
      default = "true"
    }
    DD_LOG_LEVEL = {
      default = "WARN"
    }
    DD_APPSEC_ENABLED = {
      default = "true"
    }
    DD_PROFILING_ENABLED = {
      default = "true"
    }
  }

  core = {
    S3_SYNC_IMAGE_COUNT = {
      default = 8000
    }
    SENTRY_DSN = {
      default = "https://<EMAIL>/1481735"
    }
    DB_NAME = {
      development = "core_development"
      qa          = "core_qa"
      production  = "core_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-core-service"
    }
    OKTA_URI = {
      development = "https://dev-612233.oktapreview.com/"
      qa          = "https://dev-612233.oktapreview.com/"
      production  = "https://sensehawk.okta-emea.com/"
    }
    OKTA_ISSUER_URI = {
      development = "https://dev-612233.oktapreview.com/oauth2/default"
      qa          = "https://dev-612233.oktapreview.com/oauth2/default"
      production  = "https://sensehawk.okta-emea.com/oauth2/default"
    }
    OKTA_AUDIENCE = {
      default = "api://default"
    }
    SAML_RESET_PASSWORD_TEMPLATE = {
      development = "d-24ba5cfd0c20466e8e1920854653fd3b"
      qa          = "d-24ba5cfd0c20466e8e1920854653fd3b"
      production  = "d-24ba5cfd0c20466e8e1920854653fd3b"
    }
    RESET_PASSWORD_EMAIL_TEMPLATE = {
      development = "d-87a66921fcbc4081ba166a7c415f69f3"
      qa          = "d-87a66921fcbc4081ba166a7c415f69f3"
      production  = "d-87a66921fcbc4081ba166a7c415f69f3"
    }
    INVITE_USER_EMAIL_TEMPLATE = {
      development = "d-f2ba03225f904836b77d1092476a19a2"
      qa          = "d-f2ba03225f904836b77d1092476a19a2"
      production  = "d-f2ba03225f904836b77d1092476a19a2"
    }
    SET_PASSWORD_TEMPLATE = {
      development = "d-249be16936694628b70bd755e5202d70"
      qa          = "d-249be16936694628b70bd755e5202d70"
      production  = "d-249be16936694628b70bd755e5202d70"
    }
    EMAIL_VERIFY_TEMPLATE = {
      development = "d-f456c4a653a4488aa8a5e8c57eae6b34"
      qa          = "d-f456c4a653a4488aa8a5e8c57eae6b34"
      production  = "d-f456c4a653a4488aa8a5e8c57eae6b34"
    }
    DATA_UPLOAD_STATUS_TEMPLATE = {
      development = "d-5b22e23a800646e88859cf3cc60675b8"
      qa          = "d-5b22e23a800646e88859cf3cc60675b8"
      production  = "d-5b22e23a800646e88859cf3cc60675b8"
    }
    FIREBASE_DYNAMIC_LINK_URL = {
      development = "https://taskmappernative.page.link"
      qa          = "https://taskmappernative.page.link"
      production  = "https://taskmappernative.page.link"
    }
    ANDROID_PACKAGE_NAME = {
      development = "com.sensehawk.sensehawknative"
      qa          = "com.sensehawk.sensehawknative"
      production  = "com.sensehawk.sensehawknative"
    }
    IOS_PACKAGE_NAME = {
      development = "com.sensehawk.sensehawknative"
      qa          = "com.sensehawk.sensehawknative"
      production  = "com.sensehawk.sensehawknative"
    }
    GETSTREAM_APP_ID = {
      development = "1331137"
      qa          = "88287"
      production  = "89074"
    }
    FIREBASE_DATABASE = {
      development = "https://sensehawk-stage.firebaseio.com/"
      qa          = "https://sensehawk-stage.firebaseio.com/"
      production  = "https://sensehawk-prod.firebaseio.com/"
    }
    DYNAMIC_FIREBASE_API_ENDPOINT = {
      development = "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key={}"
      qa          = "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key={}"
      production  = "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key={}"
    }
    PROCESS_RAW_IMAGES_QUEUES = {
      default = jsonencode({
        ap-south-1     = "https://sqs.ap-south-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        ap-southeast-1 = "https://sqs.ap-southeast-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        ap-southeast-2 = "https://sqs.ap-southeast-2.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        us-east-1      = "https://sqs.us-east-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        us-east-2      = "https://sqs.us-east-2.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        us-west-1      = "https://sqs.us-west-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        eu-central-1   = "https://sqs.eu-central-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
        eu-north-1     = "https://sqs.eu-north-1.amazonaws.com/${var.account_id}/${var.prefix}-process-raw-images"
      })
    }
    PILOT_BUCKETS = {
      development = jsonencode({
        ap-south-1     = ["${var.prefix}-pilot-mumbai", "sensehawk-test-pilots"]
        ap-southeast-1 = ["${var.prefix}-pilot-singapore"]
        ap-southeast-2 = ["${var.prefix}-pilot-sydney"]
        us-east-1      = ["${var.prefix}-pilot-virginia"]
        us-east-2      = ["${var.prefix}-pilot-ohio"]
        us-west-1      = ["${var.prefix}-pilot-california"]
        eu-central-1   = ["${var.prefix}-pilot-frankfurt"]
        eu-north-1     = ["${var.prefix}-pilot-stockholm"]
      })
      qa = jsonencode({
        ap-south-1     = ["${var.prefix}-pilot-mumbai", "sensehawk-test-pilots"]
        ap-southeast-1 = ["${var.prefix}-pilot-singapore"]
        ap-southeast-2 = ["${var.prefix}-pilot-sydney"]
        us-east-1      = ["${var.prefix}-pilot-virginia"]
        us-east-2      = ["${var.prefix}-pilot-ohio"]
        us-west-1      = ["${var.prefix}-pilot-california"]
        eu-central-1   = ["${var.prefix}-pilot-frankfurt"]
        eu-north-1     = ["${var.prefix}-pilot-stockholm"]
      })
      production = jsonencode({
        ap-south-1     = ["${var.prefix}-pilot-mumbai", "sh-inc-pilots"]
        ap-southeast-1 = ["${var.prefix}-pilot-singapore"]
        ap-southeast-2 = ["${var.prefix}-pilot-sydney", "sh-inc-pilots-sydney"]
        us-east-1      = ["${var.prefix}-pilot-virginia", "sh-inc-pilots-virginia"]
        us-east-2      = ["${var.prefix}-pilot-ohio", "sh-inc-pilots-americas"]
        us-west-1      = ["${var.prefix}-pilot-california"]
        eu-central-1   = ["${var.prefix}-pilot-frankfurt"]
        eu-north-1     = ["${var.prefix}-pilot-stockholm", "sh-inc-pilots-stockholm"]
      })
    }
    CORE_BUCKETS = {
      default = jsonencode([
        {
          name   = "ap-south-1"
          label  = "Mumbai"
          bucket = "${var.prefix}-core-mumbai"
        },
        {
          name   = "ap-southeast-1"
          label  = "Singapore"
          bucket = "${var.prefix}-core-singapore"
        },
        {
          name   = "ap-southeast-2"
          label  = "Sydney"
          bucket = "${var.prefix}-core-sydney"
        },
        {
          name   = "us-east-1"
          label  = "Virginia"
          bucket = "${var.prefix}-core-virginia"
        },
        {
          name   = "us-east-2"
          label  = "Ohio"
          bucket = "${var.prefix}-core-ohio"
        },
        {
          name   = "us-west-1"
          label  = "California"
          bucket = "${var.prefix}-core-california"
        },
        {
          name   = "eu-central-1"
          label  = "Frankfurt"
          bucket = "${var.prefix}-core-frankfurt"
        },
        {
          name   = "eu-north-1"
          label  = "Stockholm"
          bucket = "${var.prefix}-core-stockholm"
        }
      ])
    }
    RIL_IDPS = {
      development = ""
      qa          = ""
      production  = "0oac6r390mbQyzxiE0i7"
    }
    RIL_INLINE_URL = {
      development = ""
      qa          = ""
      production  = "https://core-server.ril.solar/api/v1/inlinehook/"
    }
    DEFAULT_ORGANIZATION = {
      development = ""
      qa          = ""
      production  = ""
    }
    DATACENTER_REGIONS = {
      default = jsonencode([
        {
          "region" : "ap-south-1",
          "label" : "Asia"
        },
        {
          "region" : "eu-central-1",
          "label" : "Europe"
        },
        {
          "region" : "us-east-1",
          "label" : "USA"
        },
        {
          "region" : "ap-southeast-2",
          "label" : "Australia"
        }
      ])
    }
  }

  terra = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/1472727"
    }
    DB_NAME = {
      development = "terra_development"
      qa          = "terra_qa"
      production  = "terra_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-terra-service"
    }
  }

  processing = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/1473006"
    }
    DB_NAME = {
      development = "processing_development"
      qa          = "processing_qa"
      production  = "processing_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-processing-service"
    }
    PROCESSING_BUCKETS = {
      default = jsonencode([
        {
          name   = "ap-south-1"
          label  = "Mumbai"
          bucket = "${var.prefix}-core-mumbai"
        },
        {
          name   = "ap-southeast-1"
          label  = "Singapore"
          bucket = "${var.prefix}-core-singapore"
        },
        {
          name   = "ap-southeast-2"
          label  = "Sydney"
          bucket = "${var.prefix}-core-sydney"
        },
        {
          name   = "us-east-1"
          label  = "Virginia"
          bucket = "${var.prefix}-core-virginia"
        },
        {
          name   = "us-east-2"
          label  = "Ohio"
          bucket = "${var.prefix}-core-ohio"
        },
        {
          name   = "us-west-1"
          label  = "California"
          bucket = "${var.prefix}-core-california"
        },
        {
          name   = "eu-central-1"
          label  = "Frankfurt"
          bucket = "${var.prefix}-core-frankfurt"
        },
        {
          name   = "eu-north-1"
          label  = "Stockholm"
          bucket = "${var.prefix}-core-stockholm"
        }
      ])
    }
  }

  inventory = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "inventory_development"
      qa          = "inventory_qa"
      production  = "inventory_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-inventory-service"
    }
    TRANSACTION_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/transaction.docx"
    }
  }

  naavix = {
    DEBUG = {
      default = "false"
    }
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "naavix_development"
      qa          = "naavix_qa"
      production  = "naavix_production"
    }
    NUM_WORKERS = {
      development = 1
      qa          = 1
      production  = 4
    }
  }

  system_model = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "system_model_development"
      qa          = "system_model_qa"
      production  = "system_model_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-system-model-service"
    }
  }

  therm = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/1471392"
    }
    DB_NAME = {
      development = "therm-b1"
      qa          = "therm-b1"
      production  = "therm-prod"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-therm-service"
    }
  }

  job_provisioner = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/1472735"
    }
    DB_NAME = {
      development = "job_provisioner_development"
      qa          = "job_provisioner_qa"
      production  = "job_provisioner_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-job-provisioner-service"
    }
    AWS_REGIONS = {
      default = "ap-south-1,ap-southeast-1,ap-southeast-2,us-east-1,us-east-2,us-west-1,eu-central-1,eu-north-1"
    }
    INFRASTRUCTURE = {
      development = jsonencode({
        "amis" = {
          "mbtiler" = {
            "ap-south-1"     = "ami-09f669a1159b629a3"
            "ap-southeast-1" = "ami-06368903cf2be7d71"
            "ap-southeast-2" = "ami-0697934937c1225e6"
            "eu-central-1"   = "ami-056dce5afe7124101"
            "eu-north-1"     = "ami-0b33ae3cd774cd56e"
            "us-east-1"      = "ami-0df45f14963c9b51f"
            "us-east-2"      = "ami-045a53f2a4761b8d6"
            "us-west-1"      = "ami-0993409453277829d"
          }
          "pix4d" = {
            "ap-south-1"     = "ami-01647e8cecce8bcac"
            "ap-southeast-1" = "ami-0c8bae3f8118d8bbc"
            "ap-southeast-2" = "ami-016a1bef2bb083fc8"
            "eu-central-1"   = "ami-08765cfc5d6145a73"
            "eu-north-1"     = "ami-04fa511b430a10bf8"
            "us-east-1"      = "ami-0071f164f7ad96f6e"
            "us-east-2"      = "ami-0eecd3e8a296c10c9"
            "us-west-1"      = "ami-091c8a5e07af24055"
          }
          "zipper" = {
            "ap-south-1"     = "ami-0dfc58d3ff88317af"
            "ap-southeast-1" = "ami-079089254e258d99c"
            "ap-southeast-2" = "ami-07adb3c9e2ab70643"
            "eu-central-1"   = "ami-0f281b47db23c1afb"
            "eu-north-1"     = "ami-0ec40a87140f4fa8e"
            "us-east-1"      = "ami-04e67cba0fe6f63b6"
            "us-east-2"      = "ami-04b3f59b40bc2979e"
            "us-west-1"      = "ami-0bdd498a0ff432582"
          }
        }
        "key_names" = {
          "ap-south-1"     = "tm-dev-keypair"
          "ap-southeast-1" = "tm-dev-keypair"
          "ap-southeast-2" = "tm-dev-keypair"
          "eu-central-1"   = "tm-dev-keypair"
          "eu-north-1"     = "tm-dev-keypair"
          "us-east-1"      = "tm-dev-keypair"
          "us-east-2"      = "tm-dev-keypair"
          "us-west-1"      = "tm-dev-keypair"
        }
        "security_groups" = {
          "ap-south-1" = [
            "sg-0dd0163e70af5dc1d",
            "sg-06292907c81d0303b",
          ]
          "ap-southeast-1" = [
            "sg-038f1c7e6019e5c50",
            "sg-03d893a31fb333713",
          ]
          "ap-southeast-2" = [
            "sg-018626336fed03a0b",
            "sg-0c3c6044474469c62",
          ]
          "eu-central-1" = [
            "sg-040be65b6ab9edb5d",
            "sg-020e7cb4b4b03020e",
          ]
          "eu-north-1" = [
            "sg-0fb3444cca3c2ad8c",
            "sg-07e7c1e8ee2d571bb",
          ]
          "us-east-1" = [
            "sg-080c779b57062f931",
            "sg-0d6669beb108a05ed",
          ]
          "us-east-2" = [
            "sg-00f21a8374e939481",
            "sg-07e2993756a3ee8cd",
          ]
          "us-west-1" = [
            "sg-0be82da4bd7502989",
            "sg-064ed484a7b2a070a",
          ]
        }
        "subnets" = {
          "ap-south-1"     = "subnet-0203e6b41356d069d"
          "ap-southeast-1" = "subnet-061eed43c122e3d0c"
          "ap-southeast-2" = "subnet-033faaa293145da8d"
          "eu-central-1"   = "subnet-0aa38a10042b23040"
          "eu-north-1"     = "subnet-02a5b1fc758b0f8c5"
          "us-east-1"      = "subnet-09022b30f685cca24"
          "us-east-2"      = "subnet-01d3272cbde3da8c1"
          "us-west-1"      = "subnet-05922e6acd8b04a7e"
        }
      })
      qa = jsonencode({
        "amis" = {
          "mbtiler" = {
            "ap-south-1"     = "ami-0ae2fd6efc6260ab5"
            "ap-southeast-1" = "ami-04f9bc4b0dc79d1d8"
            "ap-southeast-2" = "ami-00c5403b14ccb94d8"
            "eu-central-1"   = "ami-01d69aa9f1e25d71b"
            "eu-north-1"     = "ami-08461db9382201a8e"
            "us-east-1"      = "ami-06914687b4a6deab3"
            "us-east-2"      = "ami-0f2ebd119edc71189"
            "us-west-1"      = "ami-07229cfbf45ce129f"
          }
          "pix4d" = {
            "ap-south-1"     = "ami-09003250dfa6f0210"
            "ap-southeast-1" = "ami-054a3b4895f014286"
            "ap-southeast-2" = "ami-090dbcbce727c4bc0"
            "eu-central-1"   = "ami-0949357be93ceaf20"
            "eu-north-1"     = "ami-0e0f4d3251b715101"
            "us-east-1"      = "ami-08b53c28d1fb02528"
            "us-east-2"      = "ami-01f77b00790012833"
            "us-west-1"      = "ami-01473df9b3dfe37bb"
          }
          "zipper" = {
            "ap-south-1"     = "ami-086956a0ea834338f"
            "ap-southeast-1" = "ami-05a8107ccb8dbd8a8"
            "ap-southeast-2" = "ami-063b2b6d40e7faf2a"
            "eu-central-1"   = "ami-0da2651573a34fa30"
            "eu-north-1"     = "ami-01ba261a32ece6616"
            "us-east-1"      = "ami-00c5eb2615168ab15"
            "us-east-2"      = "ami-020236586c9a92d0a"
            "us-west-1"      = "ami-0aff5b43a47a75708"
          }
        }
        "key_names" = {
          "ap-south-1"     = "tm-qa-keypair"
          "ap-southeast-1" = "tm-qa-keypair"
          "ap-southeast-2" = "tm-qa-keypair"
          "eu-central-1"   = "tm-qa-keypair"
          "eu-north-1"     = "tm-qa-keypair"
          "us-east-1"      = "tm-qa-keypair"
          "us-east-2"      = "tm-qa-keypair"
          "us-west-1"      = "tm-qa-keypair"
        }
        "security_groups" = {
          "ap-south-1" = [
            "sg-0547936134a4ce5a9",
            "sg-014483d31d498e51e",
          ]
          "ap-southeast-1" = [
            "sg-02faf6fef3c5f99d7",
            "sg-039fe14208b603fb9",
          ]
          "ap-southeast-2" = [
            "sg-0d51e667e85d7b366",
            "sg-0c4950975cbe7d5f2",
          ]
          "eu-central-1" = [
            "sg-072f6dd8df5991b13",
            "sg-063c8810017b97d8b",
          ]
          "eu-north-1" = [
            "sg-07a72fba53aa9c484",
            "sg-0d62f4ce930289fa3",
          ]
          "us-east-1" = [
            "sg-0fa4554962c2cea6d",
            "sg-0a28e1b4c5702f828",
          ]
          "us-east-2" = [
            "sg-0165385cb6983cf6d",
            "sg-0e6d04a3f5899dda5",
          ]
          "us-west-1" = [
            "sg-0c6dd02f5358fef6c",
            "sg-07899290d4a12a995",
          ]
        }
        "subnets" = {
          "ap-south-1"     = "subnet-0ef44ee39789ab8c5"
          "ap-southeast-1" = "subnet-07261e7c8a6692b7d"
          "ap-southeast-2" = "subnet-0446a1b7ca7ee07ff"
          "eu-central-1"   = "subnet-06eba1a928f283104"
          "eu-north-1"     = "subnet-0d153c08962a73af3"
          "us-east-1"      = "subnet-08f39e05e18a59a50"
          "us-east-2"      = "subnet-0278980d12bd925d5"
          "us-west-1"      = "subnet-02c631a20c619b8e1"
        }
      })
      production = jsonencode({
        "amis" = {
          "mbtiler" = {
            "ap-south-1"     = "ami-0e9b7f194689bfce9"
            "ap-southeast-1" = "ami-0c09b96327bf952ea"
            "ap-southeast-2" = "ami-03e53a8b4d8654cdc"
            "eu-central-1"   = "ami-0be1c55eb31270f2c"
            "eu-north-1"     = "ami-0761513560c1806e2"
            "us-east-1"      = "ami-06653d467212f29e3"
            "us-east-2"      = "ami-03a6b77e7bd3b3c43"
            "us-west-1"      = "ami-0ef58b099011a5d18"
          }
          "pix4d" = {
            "ap-south-1"     = "ami-06e52c4bb433f24e5"
            "ap-southeast-1" = "ami-0f65d80a6ef3b42d2"
            "ap-southeast-2" = "ami-095de2e125ea38606"
            "eu-central-1"   = "ami-04637969dfc5ec43c"
            "eu-north-1"     = "ami-029cb660c0199f031"
            "us-east-1"      = "ami-06616bfa2760e5cc4"
            "us-east-2"      = "ami-049e1220220977b9b"
            "us-west-1"      = "ami-0aa9f9b8393ed81ee"
          }
          "zipper" = {
            "ap-south-1"     = "ami-0bcc26233b628986e"
            "ap-southeast-1" = "ami-024bb07a35eb814c9"
            "ap-southeast-2" = "ami-04e0ccd03dec0a497"
            "eu-central-1"   = "ami-0c6ce5697f28b38f7"
            "eu-north-1"     = "ami-070fd7344b9268973"
            "us-east-1"      = "ami-0eb8a81e88addeb6f"
            "us-east-2"      = "ami-0971c2aa2219e2d1c"
            "us-west-1"      = "ami-09a6cea16340afc2d"
          }
        }
        "key_names" = {
          "ap-south-1"     = "tm-prod-keypair"
          "ap-southeast-1" = "tm-prod-keypair"
          "ap-southeast-2" = "tm-prod-keypair"
          "eu-central-1"   = "tm-prod-keypair"
          "eu-north-1"     = "tm-prod-keypair"
          "us-east-1"      = "tm-prod-keypair"
          "us-east-2"      = "tm-prod-keypair"
          "us-west-1"      = "tm-prod-keypair"
        }
        "security_groups" = {
          "ap-south-1" = [
            "sg-01b71c29694686531",
            "sg-0c4bc6f0d7bb9444e",
          ]
          "ap-southeast-1" = [
            "sg-0b909b4ae40f9ba10",
            "sg-0d2c31795fd202995",
          ]
          "ap-southeast-2" = [
            "sg-0d4e309e0e10e0c78",
            "sg-02da508cceee94e95",
          ]
          "eu-central-1" = [
            "sg-02d1e28c43024bd8a",
            "sg-08b93b45c2eb560bb",
          ]
          "eu-north-1" = [
            "sg-098ad58ec5a656e75",
            "sg-0bea9732bcff6b520",
          ]
          "us-east-1" = [
            "sg-08d9d02631236af9c",
            "sg-03da059cc31528b5a",
          ]
          "us-east-2" = [
            "sg-0acaa629b5d1fbd4c",
            "sg-02070b2dc4cddee96",
          ]
          "us-west-1" = [
            "sg-0e94b6f5e116700ba",
            "sg-0570d993e4666f24d",
          ]
        }
        "subnets" = {
          "ap-south-1"     = "subnet-076460708a0f61532"
          "ap-southeast-1" = "subnet-03ccd54af55f46f05"
          "ap-southeast-2" = "subnet-010977a267c6881d2"
          "eu-central-1"   = "subnet-07006eaeeccd6d331"
          "eu-north-1"     = "subnet-007a0943f7f837fef"
          "us-east-1"      = "subnet-00c4bb2439de02902"
          "us-east-2"      = "subnet-0daf3d4d9c6c3442a"
          "us-west-1"      = "subnet-09a2bddc3b1a88767"
        }
      })
    }
  }

  tasks = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/1881099"
    }
    DB_NAME = {
      development = "tickets_staging"
      qa          = "tickets_staging"
      production  = "tickets"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-tasks-service"
    }
    PLAN_SNIPS_DIRECTORY = {
      default = "/location-images/plans/"
    }
    SINGLE_TASK_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/single_task.docx"
    }
    BULK_TASK_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/bulk_task.docx"
    }
    CUSTOM_TASK_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/custom_task.docx"
    }
    MAP_SNAPSHOT_URL = {
      default = "https://mapsnapshot.${var.domain}/"
    }
    MAP_SNAPSHOT_ORGANIZATIONS = {
      development = "00guyciqc0p0ZXWMl0h7,00g174ikp75l48FwW0h8,00g115xv8u4BKbtfD0h8"
      qa          = "00guyciqc0p0ZXWMl0h7,00g174ikp75l48FwW0h8,00g115xv8u4BKbtfD0h8"
      production  = "00gamx9kjpI1DXY7v0i7,00g8m87s483nVZBRS0i7,00gb25k2f2GMCblQl0i7"
    }
    PUBLIC_ATTACHMENTS_URL = {
      default = "https://p-ta.${var.domain}/"
    }
    ENABLE_AMQ = {
      default = "TRUE"
    }
    ENABLE_SQS = {
      default = "TRUE"
    }
    COMMENTS_SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-comments-service"
    }
  }

  forms = {
    BULK_EXPORT_ATTACHMENT_URL = {
      development = "https://rupdis7b47vb2c5jwxkphj2ufe0dorlq.lambda-url.us-east-1.on.aws/"
      qa          = "https://3jr5lmsb3dyodvfkf4cxettqkm0jpxhv.lambda-url.us-east-1.on.aws/"
      production  = "https://qzceyabe6b33afanrbtyt5hmle0dmnjc.lambda-url.us-east-1.on.aws/"
    }
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "forms_staging"
      qa          = "forms_staging"
      production  = "forms-production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-forms-service"
    }
    FORM_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/form.docx"
    }
    PLAN_SNIPS_DIRECTORY = {
      default = "/location-images/plans/"
    }
    ALERTS_SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-alerts-service"
    }
  }

  dms = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "dms_staging"
      qa          = "dms_staging"
      production  = "dms"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-dms-service"
    }
    TRANSMITTAL_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/transmittal.docx"
    }
    TRANSMITTAL_ANNOTATIONS_PDF_TEMPLATE = {
      default = "https://${var.prefix}-public.s3.${var.region}.amazonaws.com/templates/generate_pdf/transmittal_annotations.docx"
    }
  }

  approvals = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "approval-flow_staging"
      qa          = "approval-flow_staging"
      production  = "approval-flow_production"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-approvals-service"
    }
  }

  cnc = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "cnc_staging"
      qa          = "cnc_staging"
      production  = "cnc"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-cnc-service"
    }
  }

  onm = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "onm_staging"
      qa          = "onm_staging"
      production  = "onm"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-onm-service"
    }
  }

  integrations = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "integrations_staging"
      qa          = "integrations_staging"
      production  = "integrations"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-integrations-service"
    }
  }

  lns = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "lns_staging"
      qa          = "lns_staging"
      production  = "lns"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-lns-service"
    }
  }

  pm = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/****************"
    }
    DB_NAME = {
      development = "project-management-staging"
      qa          = "project-management-staging"
      production  = "project-management"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-pm-service"
    }
  }

  work = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/6048685"
    }
    DB_NAME = {
      development = "work_staging"
      qa          = "work_staging"
      production  = "work"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-work-service"
    }
  }

  annotations = {
    SENTRY_DSN = {
      default = "https://<EMAIL>/5509645"
    }
    DB_NAME = {
      development = "annotations_staging"
      qa          = "annotations_staging"
      production  = "annotations"
    }
    SQS_QUEUE = {
      default = "https://sqs.${var.region}.amazonaws.com/${var.account_id}/${var.prefix}-annotations-service"
    }
  }

  auth_middleware = {
    CLAIMS_ENDPOINT = {
      default = "https://api.${var.domain}/core/v1/resource/claims/"
    }
  }

  storage = {
    CORE_BUCKETS = {
      default = jsonencode({
        ap-south-1     = "${var.prefix}-core-mumbai"
        ap-southeast-1 = "${var.prefix}-core-singapore"
        ap-southeast-2 = "${var.prefix}-core-sydney"
        eu-central-1   = "${var.prefix}-core-frankfurt"
        eu-north-1     = "${var.prefix}-core-stockholm"
        us-east-1      = "${var.prefix}-core-virginia"
        us-east-2      = "${var.prefix}-core-ohio"
        us-west-1      = "${var.prefix}-core-california"
      })
    }
    APPLICATION_BUCKETS = {
      default = jsonencode({
        ap-south-1     = "${var.prefix}-asia"
        eu-central-1   = "${var.prefix}-europe"
        us-east-1      = "${var.prefix}-usa"
        ap-southeast-2 = "${var.prefix}-australia"
      })
    }
  }
}
