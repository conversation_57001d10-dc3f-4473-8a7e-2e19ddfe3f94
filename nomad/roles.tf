resource "vault_nomad_secret_role" "admin" {
  backend = vault_nomad_secret_backend.nomad.backend
  role    = "admin"
  type    = "management"
}

resource "vault_nomad_secret_role" "devops" {
  backend  = vault_nomad_secret_backend.nomad.backend
  role     = "devops"
  type     = "client"
  policies = [nomad_acl_policy.devops.name]
}

resource "vault_nomad_secret_role" "developer" {
  backend  = vault_nomad_secret_backend.nomad.backend
  role     = "developer"
  type     = "client"
  policies = [nomad_acl_policy.developer.name]
}

resource "vault_nomad_secret_role" "developer_exec" {
  backend  = vault_nomad_secret_backend.nomad.backend
  role     = "developer-exec"
  type     = "client"
  policies = [nomad_acl_policy.developer_exec.name]
}
