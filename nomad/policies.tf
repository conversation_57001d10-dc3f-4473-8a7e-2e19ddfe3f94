resource "nomad_acl_policy" "devops" {
  name      = "devops"
  rules_hcl = <<RULE
    namespace "default" {
        policy = "write"
        
        variables {
            path "*" {
                capabilities = ["read", "list", "write", "destroy"]
            }
        }
    }

    agent {
        policy = "read"
    }
    node {
        policy = "read"
    }
    quota {
        policy = "read"
    }
    RULE
}

resource "nomad_acl_policy" "developer" {
  name      = "developer"
  rules_hcl = <<RULE
    namespace "default" {
        policy = "read"
        capabilities = ["read-logs"]
        
        variables {
            path "*" {
                capabilities = ["read", "list"]
            }
        }
    }
RULE
}


resource "nomad_acl_policy" "developer_exec" {
  name      = "developer-exec"
  rules_hcl = <<RULE
    namespace "default" {
        policy = "read"
        capabilities = ["read-logs", "alloc-exec"]
        
        variables {
            path "*" {
                capabilities = ["read", "list"]
            }
        }
    }
RULE
}
