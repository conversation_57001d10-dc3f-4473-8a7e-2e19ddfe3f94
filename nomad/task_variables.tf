locals {
  services = {
    shared          = local.shared
    core            = local.core
    terra           = local.terra
    processing      = local.processing
    inventory       = local.inventory
    naavix          = local.naavix
    system_model    = local.system_model
    therm           = local.therm
    job_provisioner = local.job_provisioner
    tasks           = local.tasks
    forms           = local.forms
    dms             = local.dms
    approvals       = local.approvals
    cnc             = local.cnc
    integrations    = local.integrations
    lns             = local.lns
    pm              = local.pm
    work            = local.work
    annotations     = local.annotations
    auth_middleware = local.auth_middleware
    storage         = local.storage
    onm             = local.onm
  }
}

resource "nomad_variable" "services" {
  for_each = local.services

  path = each.key == "shared" ? "nomad/jobs" : "nomad/jobs/${each.key}"

  items = {
    for key, value in each.value : key => lookup(value, local.env, lookup(value, "default", null))
  }
}
