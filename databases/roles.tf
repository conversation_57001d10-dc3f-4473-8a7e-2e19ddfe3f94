# resource "vault_database_secret_backend_role" "core_role" {
#   backend     = vault_mount.db.path
#   name        = "postgres-core"
#   db_name     = vault_database_secret_backend_connection.core.name
#   default_ttl = 1 * 60
#   max_ttl     = 2 * 60

#   creation_statements = [
#     "GRANT scott TO vault_admin;",

#     "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}';",

#     "GRANT CONNECT ON DATABASE core_prod TO \"{{name}}\";",
#     "GRANT USAGE ON SCHEMA public TO \"{{name}}\";",

#     "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO \"{{name}}\";",
#     "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO \"{{name}}\";",
#     "GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO \"{{name}}\";",

#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott GRANT ALL PRIVILEGES ON TABLES TO \"{{name}}\";",
#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott GRANT ALL PRIVILEGES ON SEQUENCES TO \"{{name}}\";",
#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott GRANT ALL PRIVILEGES ON FUNCTIONS TO \"{{name}}\";"
#   ]

#   revocation_statements = [
#     "REVOKE CONNECT ON DATABASE core_prod FROM \"{{name}}\";",
#     "REVOKE USAGE ON SCHEMA public FROM \"{{name}}\";",

#     "REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM \"{{name}}\";",
#     "REVOKE ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public FROM \"{{name}}\";",
#     "REVOKE ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public FROM \"{{name}}\";",

#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott REVOKE ALL PRIVILEGES ON TABLES FROM \"{{name}}\";",
#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott REVOKE ALL PRIVILEGES ON SEQUENCES FROM \"{{name}}\";",
#     "ALTER DEFAULT PRIVILEGES IN SCHEMA public FOR ROLE scott REVOKE ALL PRIVILEGES ON FUNCTIONS FROM \"{{name}}\";",

#     "DROP ROLE \"{{name}}\";",
#   ]
# }


locals {
  postgres_services = ["core", "terra", "inventory", "system_model", "processing", "naavix", "readonly"]
  mongo_services    = ["therm", "tasks", "forms", "dms", "annotations", "pm", "approvals", "integrations", "lns", "cnc", "work", "job_provisioner", "onm"]
}

resource "vault_database_secret_backend_static_role" "postgres" {
  for_each          = toset(local.postgres_services)
  backend           = vault_mount.postgres.path
  name              = "${each.value}-service"
  db_name           = vault_database_secret_backend_connection.postgres.name
  username          = "${each.value}_service"
  rotation_schedule = "0 11 1 1 *" # every 1st January at 11am UTC i.e 3AM PST
}

resource "vault_database_secret_backend_static_role" "mongo" {
  for_each          = toset(local.mongo_services)
  backend           = vault_mount.mongo.path
  name              = "${each.value}-service"
  db_name           = vault_database_secret_backend_connection.mongo.name
  username          = "${each.value}_service"
  rotation_schedule = "0 11 1 1 *" # every 1st January at 11am UTC i.e 3AM PST
}

resource "vault_database_secret_backend_role" "read-only-role" {
  backend             = vault_mount.mongo.path
  name                = "read-only"
  db_name             = vault_database_secret_backend_connection.mongo.name
  creation_statements = ["{\"databaseName\": \"admin\",\"roles\": [{\"databaseName\": \"admin\",\"roleName\": \"readAnyDatabase\"}]}"]
  default_ttl         = 86400
  max_ttl             = 604800
}
resource "vault_database_secret_backend_role" "read-write-role" {
  backend             = vault_mount.mongo.path
  name                = "read-write"
  db_name             = vault_database_secret_backend_connection.mongo.name
  creation_statements = ["{\"databaseName\": \"admin\",\"roles\": [{\"databaseName\": \"admin\",\"roleName\": \"readWriteAnyDatabase\"}]}"]
  default_ttl         = 86400
  max_ttl             = 604800
}
resource "vault_database_secret_backend_role" "admin-role" {
  backend             = vault_mount.mongo.path
  name                = "admin"
  db_name             = vault_database_secret_backend_connection.mongo.name
  creation_statements = ["{\"databaseName\": \"admin\",\"roles\": [{\"databaseName\": \"admin\",\"roleName\": \"atlasAdmin\"}]}"]
  default_ttl         = 86400
  max_ttl             = 604800
}