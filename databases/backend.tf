resource "vault_mount" "postgres" {
  path        = "postgres"
  type        = "database"
  description = "Manage PostgreSQL database credentials"
}

resource "vault_database_secret_backend_connection" "postgres" {
  backend       = vault_mount.postgres.path
  name          = "postgres"
  allowed_roles = ["*"]

  postgresql {
    connection_url = "postgresql://{{username}}:{{password}}@${var.postgres_host}:5432/postgres"
    username       = var.postgres_username
    password       = var.postgres_password
  }
}

resource "vault_mount" "mongo" {
  path        = "mongo"
  type        = "database"
  description = "Manage Mongodb Atlas database credentials"
}

resource "vault_database_secret_backend_connection" "mongo" {
  backend       = vault_mount.mongo.path
  name          = "mongo"
  allowed_roles = ["*"]

  mongodbatlas {
    project_id  = var.mongodb_atlas_project_id
    public_key  = var.mongodb_atlas_public_key
    private_key = var.mongodb_atlas_private_key
  }
}
