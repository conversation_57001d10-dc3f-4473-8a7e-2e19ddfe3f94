locals {
  env        = split("-", terraform.workspace)[1]
  env_prefix = local.env == "production" ? "-prod" : ""
}

data "aws_route53_zone" "primary" {
  name = var.domain
}

resource "aws_route53_record" "app" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "app.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["app-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "app_old" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "app-old.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["app-old-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "terra_viewer" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "terra-viewer.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["terra-viewer-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "therm_viewer" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "therm-viewer.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["therm-viewer-${replace(var.domain, ".com", "")}.netlify.app"]
}


resource "aws_route53_record" "qc_viewer" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "qc-viewer.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["qc-viewer-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "dashboard" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "dashboard.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["dashboard-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "mapsnapshot" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "mapsnapshot.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["mapsnapshot-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "ticket_attachments" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "p-ta.${var.domain}"
  type    = "CNAME"
  ttl     = 300

  records = ["p-ta-${replace(var.domain, ".com", "")}.netlify.app"]
}

resource "aws_route53_record" "app_aliases" {
  for_each = toset(var.app_aliases)
  zone_id  = data.aws_route53_zone.primary.zone_id
  name     = "${each.key}.${var.domain}"
  type     = "CNAME"
  ttl      = 300

  records = ["${each.key}-${replace(var.domain, ".com", "")}.netlify.app"]
}
