{"Version": "2012-10-17", "Statement": [{"Sid": "BucketsAccess", "Effect": "Allow", "Action": ["s3:ListBucket", "s3:GetObject", "s3:PutObject", "s3:PutObjectAcl", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::*"]}, {"Sid": "CoreSQSAccess", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes"], "Resource": ["arn:aws:sqs:${region}:${account_id}:${prefix}-core-service"]}, {"Sid": "ProcessQueueAccess", "Effect": "Allow", "Action": ["sqs:SendMessage"], "Resource": ["arn:aws:sqs:ap-south-1:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:ap-southeast-1:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:ap-southeast-2:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:eu-central-1:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:us-east-1:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:us-west-1:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:us-east-2:${account_id}:${prefix}-process-raw-images", "arn:aws:sqs:eu-north-1:${account_id}:${prefix}-process-raw-images"]}, {"Sid": "FederationTokenAccess", "Effect": "Allow", "Action": ["sts:GetFederationToken"], "Resource": "*"}]}