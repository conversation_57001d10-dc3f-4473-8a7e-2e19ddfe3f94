{"Version": "2012-10-17", "Statement": [{"Sid": "AllowCoreBucketsAccess", "Effect": "Allow", "Action": ["s3:ListBucket", "s3:GetObject", "s3:PutObject", "s3:PutObjectAcl", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::*"]}, {"Sid": "AllowSQSAccess", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes"], "Resource": ["arn:aws:sqs:${region}:${account_id}:${prefix}-processing-service"]}, {"Sid": "GetFederationToken", "Effect": "Allow", "Action": ["sts:GetFederationToken"], "Resource": "*"}]}