{"Version": "2012-10-17", "Statement": [{"Sid": "PivotReportData", "Effect": "Allow", "Action": ["s3:PutObject", "s3:PutObjectAcl"], "Resource": ["arn:aws:s3:::${prefix}-primary/integrations/actions/*"]}, {"Sid": "AllowSQSAccess", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes"], "Resource": ["arn:aws:sqs:${region}:${account_id}:${prefix}-integrations-service"]}]}