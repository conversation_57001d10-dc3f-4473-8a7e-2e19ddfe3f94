variable "prefix" {
  type        = string
  description = "Environment prefix"
}

variable "domain" {
  type        = string
  description = "Domain name of the application"
}

variable "app_aliases" {
  type        = list(string)
  description = "List of app aliases to create DNS entries for"
  default     = []
}

variable "region" {
  type        = string
  description = "AWS Region of the primary infrastructure"
}

variable "account_id" {
  type        = string
  description = "AWS Account ID"
}

variable "aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID"
  sensitive   = false
}

variable "aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key"
  sensitive   = true
}

variable "core_old_buckets" {
  type        = list(string)
  description = "List of old core bucket names"
}

variable "pilot_old_buckets" {
  type        = list(string)
  description = "List of old pilot buckets"
}

variable "mbtiles_old_bucket" {
  type        = string
  description = "Name of the old mbtiles bucket"
}

variable "public_old_bucket" {
  type        = string
  description = "Name of the old public bucket"
}

variable "jobprovisioner_aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID for jobprovisioner"
  sensitive   = false
}

variable "jobprovisioner_aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key for jobprovisioner"
  sensitive   = true
}

variable "pilot_aws_access_key_id" {
  type        = string
  description = "AWS Access Key ID for pilot"
  sensitive   = false
}

variable "pilot_aws_secret_access_key" {
  type        = string
  description = "AWS Secret Access Key for pilot"
  sensitive   = true
}
