locals {
  static_iam_users = ["core-service", "processing-service", "storage-service"]
  services = [
    "approvals",
    "core",
    "dashboard_exporter",
    "dms",
    "forms",
    "integrations",
    "inventory",
    "lns",
    "pm",
    "processing",
    "storage",
    "tasks",
    "terra",
    "offline-sync-error-handler",
    "naavix"
  ]
}


resource "aws_iam_user" "service" {
  for_each = toset(local.services)
  name     = "${var.prefix}-${each.key}-service"
  path     = "/vault-role/"
}

resource "aws_iam_policy" "service" {
  for_each    = toset(local.services)
  name        = "${var.prefix}-${each.key}-service"
  description = "Policy for ${each.key} IAM user"

  policy = templatefile("${path.module}/policies/${each.key}-service.tpl", {
    prefix     = var.prefix,
    region     = var.region,
    account_id = var.account_id
  })
}


resource "aws_iam_user_policy_attachment" "service_policy_attachment" {
  for_each   = toset(local.services)
  user       = aws_iam_user.service[each.key].name
  policy_arn = aws_iam_policy.service[each.key].arn
}

resource "vault_aws_secret_backend_static_role" "service" {
  for_each        = toset(local.services)
  backend         = vault_aws_secret_backend.aws.path
  name            = "${each.key}-service"
  username        = "${var.prefix}-${each.key}-service"
  rotation_period = 7 * 24 * 60 * 60
}

resource "vault_aws_secret_backend_static_role" "job_provisioner_service" {
  backend         = vault_aws_secret_backend.aws_jobprovisioner.path
  name            = "job_provisioner-service"
  username        = "${var.prefix}-job_provisioner-service"
  rotation_period = 7 * 24 * 60 * 60
}

resource "vault_aws_secret_backend_static_role" "pilot-admin" {
  backend         = vault_aws_secret_backend.aws_pilot.path
  name            = "pilot-admin"
  username        = "${var.prefix}-pilot-admin"
  rotation_period = 90 * 24 * 60 * 60
}
