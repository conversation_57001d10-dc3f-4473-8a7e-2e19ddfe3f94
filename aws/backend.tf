resource "vault_aws_secret_backend" "aws" {
  description               = "Manage AWS credentials for primary account"
  access_key                = var.aws_access_key_id
  secret_key                = var.aws_secret_access_key
  default_lease_ttl_seconds = 7 * 24 * 60 * 60
  max_lease_ttl_seconds     = 30 * 24 * 60 * 60

  lifecycle {
    ignore_changes = [access_key, secret_key]
  }
}

resource "vault_aws_secret_backend" "aws_jobprovisioner" {
  path                      = "aws-jobprovisioner"
  description               = "Manage AWS credentials for Job provisioner account"
  access_key                = var.jobprovisioner_aws_access_key_id
  secret_key                = var.jobprovisioner_aws_secret_access_key
  default_lease_ttl_seconds = 7 * 24 * 60 * 60
  max_lease_ttl_seconds     = 30 * 24 * 60 * 60

  lifecycle {
    ignore_changes = [access_key, secret_key]
  }
}

resource "vault_aws_secret_backend" "aws_pilot" {
  path                      = "aws-pilot"
  description               = "Manage AWS credentials for pilot account"
  access_key                = var.pilot_aws_access_key_id
  secret_key                = var.pilot_aws_secret_access_key
  default_lease_ttl_seconds = 7 * 24 * 60 * 60
  max_lease_ttl_seconds     = 30 * 24 * 60 * 60

  lifecycle {
    ignore_changes = [access_key, secret_key]
  }
}
