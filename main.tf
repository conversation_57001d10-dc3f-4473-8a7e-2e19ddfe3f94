terraform {
  # Note: Set TF_CLOUD_ORGANIZATION, TF_CLOUD_HOSTNAME, TF_WORKSPACE environment variables.
  cloud {}
}

provider "aws" {
  default_tags {
    tags = {
      CreatedBy = "terraform"
    }
  }
}

provider "vault" {
  address = "https://vault.${var.domain}"
}

module "aws" {
  source                               = "./aws"
  domain                               = var.domain
  app_aliases                          = var.app_aliases
  aws_access_key_id                    = var.aws_access_key_id
  aws_secret_access_key                = var.aws_secret_access_key
  jobprovisioner_aws_access_key_id     = var.jobprovisioner_aws_access_key_id
  jobprovisioner_aws_secret_access_key = var.jobprovisioner_aws_secret_access_key
  pilot_aws_access_key_id              = var.pilot_aws_access_key_id
  pilot_aws_secret_access_key          = var.pilot_aws_secret_access_key
  prefix                               = var.prefix
  region                               = var.region
  account_id                           = var.account_id
  core_old_buckets                     = var.core_old_buckets
  pilot_old_buckets                    = var.pilot_old_buckets
  mbtiles_old_bucket                   = var.mbtiles_old_bucket
  public_old_bucket                    = var.public_old_bucket
}

module "kv2_secrets_engine" {
  source       = "./kv2"
  domain       = var.domain
  secrets_name = var.secrets_name
}

provider "consul" {
  address = "https://consul.${var.domain}"
}

module "consul" {
  source                  = "./consul"
  domain                  = var.domain
  vault_consul_http_token = var.vault_consul_http_token
}

# NOTE: Update values and re-run after complete database migration
module "databases" {
  source                    = "./databases"
  postgres_host             = var.postgres_host
  postgres_username         = var.postgres_username
  postgres_password         = var.postgres_password
  mongodb_atlas_project_id  = var.mongodb_atlas_project_id
  mongodb_atlas_public_key  = var.mongodb_atlas_public_key
  mongodb_atlas_private_key = var.mongodb_atlas_private_key
}

provider "nomad" {
  address = "https://nomad.${var.domain}"
}

module "nomad" {
  source            = "./nomad"
  prefix            = var.prefix
  region            = var.region
  account_id        = var.account_id
  domain            = var.domain
  vault_nomad_token = var.vault_nomad_token
}

module "vault_policies" {
  source = "./vault_policies"
}

module "ssh" {
  source       = "./ssh"
  secrets_name = var.secrets_name
}

module "okta" {
  source       = "./okta"
  prefix       = var.prefix
  api_token    = var.okta_api_token
  organization = var.okta_organization
  base_url     = var.okta_base_url
}
